<template>
  <div class="group-user-panel">
    <div class="panel-header">
      <h4>群成员 ({{ loading ? '加载中...' : members.length }})</h4>
    </div>

    <div class="member-list">
      <el-scrollbar class="member-scrollbar">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>正在加载群成员...</span>
        </div>

        <!-- 空状态 -->
        <div v-else-if="members.length === 0" class="empty-container">
          <el-icon><UserFilled /></el-icon>
          <span>暂无群成员</span>
        </div>

        <!-- 成员列表 -->
        <div v-else class="member-item" v-for="member in members" :key="member.id">
          <div class="member-info" @click="handleMemberClick(member)">
            <div class="avatar-wrapper">
              <ChatAvatar
                :size="32"
                :user-info="member"
                :clickable="true"
                @click="handleMemberClick(member)"
              />
              <div
                v-if="member.online"
                class="online-dot"
                title="在线"
              ></div>
            </div>

            <div class="member-details">
              <div class="member-name">
                <span class="nickname-text">{{ member.nickname }}</span>
                <el-tag v-if="member.isOwner" size="small" type="danger">群主</el-tag>
                <el-tag v-else-if="member.isAdmin" size="small" type="warning">管理员</el-tag>
              </div>
              <div class="member-status">
                <span :class="member.online ? 'online' : 'offline'">
                  {{ member.online ? '在线' : '离线' }}
                </span>
                <span v-if="member.phone" class="phone-info">{{ member.phone }}</span>
              </div>
            </div>
          </div>
          
          <el-dropdown 
            @command="handleCommand"
            trigger="click"
            class="member-actions"
          >
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="`chat_${member.id}`">
                  <el-icon><ChatDotRound /></el-icon>
                  私聊
                </el-dropdown-item>
                <el-dropdown-item :command="`profile_${member.id}`">
                  <el-icon><User /></el-icon>
                  查看资料
                </el-dropdown-item>
                <!-- <el-dropdown-item 
                  v-if="canManage && !member.isAdmin" 
                  :command="`admin_${member.id}`"
                  divided
                >
                  <el-icon><Star /></el-icon>
                  设为管理员
                </el-dropdown-item> -->
                <!-- <el-dropdown-item 
                  v-if="canManage" 
                  :command="`kick_${member.id}`"
                  class="danger-item"
                >
                  <el-icon><Remove /></el-icon>
                  移出群聊
                </el-dropdown-item> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-scrollbar>
    </div>

    <!-- 用户资料弹窗 -->
    <el-dialog
      v-model="profileDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      draggable
      append-to-body
    >
      <div v-if="profileLoading" class="profile-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>正在加载用户资料...</span>
      </div>

      <div v-else-if="currentUserProfile" class="user-profile">
        <el-descriptions :column="1" border class="profile-details">
          <el-descriptions-item label="用户ID">
            {{ currentUserProfile.id }}
          </el-descriptions-item>
          <el-descriptions-item label="昵称">
            {{ currentUserProfile.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号码">
            {{ currentUserProfile.iphoneNum || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="头像">
            <el-image
              v-if="currentUserProfile.headImg"
              :src="currentUserProfile.headImg"
              :preview-src-list="[currentUserProfile.headImg]"
              fit="cover"
              style="width: 50px; height: 50px; border-radius: 4px;"
            />
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="加入的群组">
            {{ currentUserProfile.groups || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="在线状态">
            <el-tag :type="currentUserProfile.online ? 'success' : 'info'" size="small">
              {{ currentUserProfile.online ? '在线' : '离线' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否管理员">
            <el-tag :type="currentUserProfile.isAdmin ? 'warning' : 'info'" size="small">
              {{ currentUserProfile.isAdmin ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{ formatDateTime(currentUserProfile.lastLoginTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentUserProfile.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更多详情">
            {{ currentUserProfile.moreInfo || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button @click="profileDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getGroupMembers } from '@/utils/groupMemberUtils'
import ChatAvatar from '@/components/ChatAvatar/index.vue'
import { findImUser } from '@/api/im/imuser'

defineOptions({
  name: 'GroupUserPanel'
})

const props = defineProps({
  group: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['member-click', 'start-chat'])

// 群成员数据
const members = ref([])
const loading = ref(false)

// 用户资料弹窗相关
const profileDialogVisible = ref(false)
const currentUserProfile = ref(null)
const profileLoading = ref(false)

// 获取群成员信息
const fetchGroupMembers = async (useCache = true) => {
  if (!props.group) {
    console.warn('群组信息为空')
    members.value = []
    return
  }

  loading.value = true
  try {
    const groupMembers = await getGroupMembers(props.group, useCache)
    members.value = groupMembers

    if (groupMembers.length === 0) {
      ElMessage.warning('该群组暂无成员')
    }
  } catch (error) {
    console.error('获取群成员信息出错:', error)
    ElMessage.error('获取群成员信息失败')
    members.value = []
  } finally {
    loading.value = false
  }
}

// 刷新群成员（不使用缓存）
const refreshGroupMembers = () => {
  fetchGroupMembers(false)
}

// 当前用户是否有管理权限
const canManage = computed(() => {
  // 这里应该根据实际的权限逻辑判断
  return true
})

// 监听群组变化，重新获取成员信息
watch(() => props.group, (newGroup) => {
  if (newGroup && newGroup.FromID) {
    fetchGroupMembers()
  }
}, { immediate: true, deep: true })

// 组件挂载时获取成员信息
onMounted(() => {
  if (props.group && props.group.FromID) {
    fetchGroupMembers()
  }
})

// 处理成员点击
const handleMemberClick = (member) => {
  emit('member-click', member)
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  console.log('handleCommand:', command)

  const [action, userId] = command.split('_')
  const member = members.value.find(m => m.id == userId) // 使用 == 避免类型不匹配

  if (!member) {
    console.error('未找到成员，userId:', userId, '成员列表:', members.value.map(m => ({ id: m.id, nickname: m.nickname })))
    ElMessage.error('未找到对应的成员信息')
    return
  }

  switch (action) {
    case 'chat':
      startPrivateChat(member)
      break
    case 'profile':
      console.log('显示用户资料，member:', member)
      showMemberProfile(member)
      break
    default:
      console.warn('未知操作:', action)
  }
}

// 开始私聊
const startPrivateChat = (member) => {
  emit('start-chat', member)
  ElMessage.success(`开始与 ${member.nickname} 的私聊`)
}

// 查看成员资料
const showMemberProfile = async (member) => {
  console.log('showMemberProfile:', member.id, member.nickname)

  try {
    profileLoading.value = true
    profileDialogVisible.value = true

    // 调用API获取用户详细信息
    console.log('调用API: /api/imuser/findImUser?id=' + member.id)
    const response = await findImUser({ id: member.id })
    console.log('API响应:', response)

    if (response.code === 0) {
      currentUserProfile.value = response.data
      console.log('用户资料加载成功')
    } else {
      console.error('API错误:', response.code, response.msg)
      ElMessage.error('获取用户资料失败: ' + (response.msg || '未知错误'))
      profileDialogVisible.value = false
    }
  } catch (error) {
    console.error('请求失败:', error)
    ElMessage.error('获取用户资料失败: ' + error.message)
    profileDialogVisible.value = false
  } finally {
    profileLoading.value = false
  }
}

// 格式化时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'

  try {
    const date = new Date(dateTimeStr)

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return dateTimeStr // 如果无法解析，返回原始字符串
    }

    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    console.error('时间格式化错误:', error)
    return dateTimeStr
  }
}

</script>

<style lang="scss" scoped>
.group-user-panel {
  width: 240px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #374151;
  color: #f3f4f6;
  flex-shrink: 0;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #6b7280;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #f3f4f6;
  }

  .el-button {
    color: #9ca3af;

    &:hover {
      color: #f3f4f6;
    }
  }
}

.member-list {
  flex: 1;
  overflow: hidden;

  .member-scrollbar {
    height: 100%;
  }

  .loading-container,
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #9ca3af;
    font-size: 14px;

    .el-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(75, 85, 99, 0.6);
  }

  .member-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;

    .avatar-wrapper {
      position: relative;

      .online-dot {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        background: #22c55e;
        border: 2px solid #374151;
        border-radius: 50%;
      }
    }

    .member-details {
      flex: 1;
      min-width: 0;

      .member-name {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;
        width: 100%;
        min-width: 0; // 确保容器可以收缩

        .nickname-text {
          max-width: 120px; // 固定昵称最大宽度
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
          flex-shrink: 1;
        }

        .el-tag {
          font-size: 10px;
          height: 16px;
          line-height: 14px;
          flex-shrink: 0; // 标签不收缩，保持完整显示
        }
      }

      .member-status {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
        overflow: hidden;
        min-width: 0;

        .online {
          color: #22c55e;
          flex-shrink: 0;
        }

        .offline {
          color: #9ca3af;
          flex-shrink: 0;
        }

        .phone-info {
          color: #6b7280;
          font-size: 11px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
        }
      }
    }
  }

  .member-actions {
    .el-button {
      color: #9ca3af;

      &:hover {
        color: #f3f4f6;
      }
    }
  }
}

:deep(.el-dropdown-menu__item) {
  &.danger-item {
    color: #e74c3c;

    &:hover {
      background: #e74c3c;
      color: white;
    }
  }
}

// 用户资料弹窗样式
.profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #9ca3af;
  font-size: 14px;

  .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
}

.user-profile {
  .profile-details {
    :deep(.el-descriptions__label) {
      width: 120px;
      font-weight: 500;
    }
  }
}
</style>
